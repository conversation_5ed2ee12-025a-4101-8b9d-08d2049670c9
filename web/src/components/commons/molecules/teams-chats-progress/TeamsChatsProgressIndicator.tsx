import * as React from 'react';
import {
  ProgressIndicator, Text, Stack, MessageBar, MessageBarType,
} from '@fluentui/react';

export interface TeamsChatsProgressIndicatorProps {
  /** 進行中かどうか */
  isInProgress: boolean;
  /** 現在の進捗（0-1） */
  progress?: number;
  /** 進捗テキスト */
  progressText?: string;
  /** 完了メッセージ */
  completedMessage?: string;
  /** エラーメッセージ */
  errorMessage?: string;
  /** エラーをクリアする関数 */
  onClearError?: () => void;
  /** 詳細情報 */
  details?: {
    total: number;
    completed: number;
    failed: number;
  };
}

/**
 * TeamsChats保存処理の進捗を表示するインジケーター
 */
const TeamsChatsProgressIndicator: React.FC<TeamsChatsProgressIndicatorProps> = ({
  isInProgress,
  progress,
  progressText,
  completedMessage,
  errorMessage,
  onClearError,
  details,
}) => {
  // 自動的にメッセージを非表示にするタイマー
  const [showCompleted, setShowCompleted] = React.useState(false);
  const completedTimeoutRef = React.useRef<NodeJS.Timeout | null>(null);

  // 完了メッセージの表示制御
  React.useEffect(() => {
    if (completedMessage && !isInProgress && !errorMessage) {
      setShowCompleted(true);

      // 3秒後に自動的に非表示
      completedTimeoutRef.current = setTimeout(() => {
        setShowCompleted(false);
      }, 3000);
    } else {
      setShowCompleted(false);
    }

    return () => {
      if (completedTimeoutRef.current) {
        clearTimeout(completedTimeoutRef.current);
      }
    };
  }, [completedMessage, isInProgress, errorMessage]);

  // 進捗率の計算
  const calculatedProgress = React.useMemo(() => {
    if (progress !== undefined) {
      return progress;
    }

    if (details && details.total > 0) {
      return (details.completed + details.failed) / details.total;
    }

    return undefined;
  }, [progress, details]);

  // 進捗テキストの生成
  const displayText = React.useMemo(() => {
    if (progressText) {
      return progressText;
    }

    if (details) {
      const { total, completed, failed } = details;
      if (failed > 0) {
        return `${completed}件完了、${failed}件失敗 (${total}件中)`;
      }
      return `${completed}件完了 (${total}件中)`;
    }

    if (isInProgress) {
      return '保存中...';
    }

    return '';
  }, [progressText, details, isInProgress]);

  if (!isInProgress && !showCompleted && !errorMessage) {
    return null;
  }

  return (
    <Stack tokens={{ childrenGap: 8 }}>
      {/* エラー表示 */}
      {errorMessage && (
        <MessageBar
          messageBarType={MessageBarType.error}
          onDismiss={onClearError}
          dismissButtonAriaLabel="エラーを閉じる"
        >
          <Text variant="small">
            <strong>保存エラー:</strong>
            {' '}
            {errorMessage}
          </Text>
        </MessageBar>
      )}

      {/* 完了メッセージ */}
      {showCompleted && completedMessage && !errorMessage && (
        <MessageBar
          messageBarType={MessageBarType.success}
          onDismiss={() => setShowCompleted(false)}
          dismissButtonAriaLabel="メッセージを閉じる"
        >
          <Text variant="small">
            {completedMessage}
          </Text>
        </MessageBar>
      )}

      {/* 進捗表示 */}
      {isInProgress && (
        <Stack tokens={{ childrenGap: 4 }}>
          <ProgressIndicator
            percentComplete={calculatedProgress}
            description={displayText}
            barHeight={4}
          />

          {/* 詳細情報 */}
          {details && details.total > 0 && (
            <Stack horizontal horizontalAlign="space-between">
              <Text variant="small" style={{ color: '#666' }}>
                進捗:
                {' '}
                {Math.round((calculatedProgress || 0) * 100)}
                %
              </Text>

              {details.failed > 0 && (
                <Text variant="small" style={{ color: '#d13438' }}>
                  {details.failed}
                  件のエラー
                </Text>
              )}
            </Stack>
          )}
        </Stack>
      )}
    </Stack>
  );
};

TeamsChatsProgressIndicator.defaultProps = {
  /** 現在の進捗（0-1） */
  progress: 0,
  /** 進捗テキスト */
  progressText: undefined,
  /** 完了メッセージ */
  completedMessage: undefined,
  /** エラーメッセージ */
  errorMessage: undefined,
  /** エラーをクリアする関数 */
  onClearError: undefined,
  /** 詳細情報 */
  details: {
    total: 0,
    completed: 0,
    failed: 0,
  },
};

export default TeamsChatsProgressIndicator;
