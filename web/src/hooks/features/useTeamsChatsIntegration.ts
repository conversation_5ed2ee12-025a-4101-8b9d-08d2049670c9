import * as React from 'react';
import { EventReporter } from '@avanade-teams/app-insights-reporter';
import useIndexedDbAccessor from '../accessors/useIndexedDbAccessor';
import useTeamsChatsRepositoryAccessor from '../accessors/useTeamsChatsRepositoryAccessor';
import useTeamsChatsApiAccessor from '../accessors/useTeamsChatsApiAccessor';
import useRemoteTeamsChatsFeature from './useRemoteTeamsChatsFeature';
import useTeamsChatsBackgroundSync from './useTeamsChatsBackgroundSync';
import { IUserChatItem } from '../accessors/useUserChatsAndChannelsAccessor';
import { ITeamsChatsItem } from '../../types/IGeraniumAttaneDB';

export type UseTeamsChatsIntegrationReturnType = {
  // データ
  allTeamsChats: ITeamsChatsItem[];
  isInitialized: boolean;
  
  // 同期状態
  isOnline: boolean;
  isSyncing: boolean;
  isTransactionPending: boolean;
  lastSyncTime: Date | null;
  queueCount: number;
  
  // 操作
  saveSelectedItems: (items: IUserChatItem[]) => Promise<void>;
  deleteTeamsChatsItem: (item: ITeamsChatsItem) => Promise<void>;
  forceSyncNow: () => Promise<void>;
  
  // バックグラウンド同期制御
  startBackgroundSync: () => void;
  stopBackgroundSync: () => void;
  
  // エラー状態
  lastError: Error | null;
  clearError: () => void;
};

/**
 * TeamsChats機能の統合フック
 * IndexedDB、リモート同期、バックグラウンド同期を統合して提供
 */
const useTeamsChatsIntegration = (
  eventReporter: EventReporter,
): UseTeamsChatsIntegrationReturnType => {
  // エラー状態管理
  const [lastError, setLastError] = React.useState<Error | null>(null);

  // IndexedDBアクセサー
  const [openDB] = useIndexedDbAccessor();

  // リポジトリアクセサー
  const repositoryReturn = useTeamsChatsRepositoryAccessor(openDB);
  const {
    allTeamsChats,
    isInitialized,
    isTransactionPending,
  } = repositoryReturn;

  // APIアクセサー（tokenProviderとreportersは外部から注入される想定）
  const apiReturn = useTeamsChatsApiAccessor(
    undefined, // tokenProviderは外部から注入
    [eventReporter], // reporters
  );

  // リモート同期機能
  const remoteFeatureReturn = useRemoteTeamsChatsFeature(
    repositoryReturn,
    apiReturn,
    eventReporter,
  );
  const {
    addMultipleRemoteTeamsChats,
    deleteRemoteTeamsChats,
    convertUserChatItems,
  } = remoteFeatureReturn;

  // バックグラウンド同期機能
  const backgroundSyncReturn = useTeamsChatsBackgroundSync(
    repositoryReturn,
    apiReturn,
    eventReporter,
  );
  const {
    isOnline,
    isSyncing,
    lastSyncTime,
    queueCount,
    startBackgroundSync,
    stopBackgroundSync,
    forceSyncNow,
  } = backgroundSyncReturn;

  /**
   * エラーをクリアする
   */
  const clearError = React.useCallback(() => {
    setLastError(null);
  }, []);

  /**
   * 選択されたアイテムを保存する
   */
  const saveSelectedItems = React.useCallback(async (items: IUserChatItem[]) => {
    if (!addMultipleRemoteTeamsChats) {
      const error = new Error('TeamsChats機能が初期化されていません');
      setLastError(error);
      throw error;
    }

    try {
      clearError();
      const teamsChatsItems = convertUserChatItems(items);
      await addMultipleRemoteTeamsChats(teamsChatsItems);
    } catch (error) {
      const err = error as Error;
      setLastError(err);
      throw err;
    }
  }, [addMultipleRemoteTeamsChats, convertUserChatItems, clearError]);

  /**
   * TeamsChatsアイテムを削除する
   */
  const deleteTeamsChatsItem = React.useCallback(async (item: ITeamsChatsItem) => {
    if (!deleteRemoteTeamsChats) {
      const error = new Error('TeamsChats機能が初期化されていません');
      setLastError(error);
      throw error;
    }

    try {
      clearError();
      await deleteRemoteTeamsChats(item);
    } catch (error) {
      const err = error as Error;
      setLastError(err);
      throw err;
    }
  }, [deleteRemoteTeamsChats, clearError]);

  /**
   * 手動同期（エラーハンドリング付き）
   */
  const forceSyncNowWithErrorHandling = React.useCallback(async () => {
    try {
      clearError();
      await forceSyncNow();
    } catch (error) {
      const err = error as Error;
      setLastError(err);
      throw err;
    }
  }, [forceSyncNow, clearError]);

  // 初期化完了後にバックグラウンド同期を開始
  React.useEffect(() => {
    if (isInitialized && openDB) {
      startBackgroundSync();
    }

    return () => {
      stopBackgroundSync();
    };
  }, [isInitialized, openDB, startBackgroundSync, stopBackgroundSync]);

  return {
    // データ
    allTeamsChats,
    isInitialized,
    
    // 同期状態
    isOnline,
    isSyncing,
    isTransactionPending,
    lastSyncTime,
    queueCount,
    
    // 操作
    saveSelectedItems,
    deleteTeamsChatsItem,
    forceSyncNow: forceSyncNowWithErrorHandling,
    
    // バックグラウンド同期制御
    startBackgroundSync,
    stopBackgroundSync,
    
    // エラー状態
    lastError,
    clearError,
  };
};

export default useTeamsChatsIntegration;
